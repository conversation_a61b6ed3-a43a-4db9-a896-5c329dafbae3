# 🔍 Audit Summary Microservice

> **Sistema inteligente de auditoria e análise de atendimentos usando IA avançada**

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![AWS](https://img.shields.io/badge/AWS-Lambda%20%7C%20RDS-orange.svg)](https://aws.amazon.com)
[![AI](https://img.shields.io/badge/AI-OpenAI%20%7C%20Claude%20%7C%20Bedrock-purple.svg)](https://openai.com)

## 📋 Visão Geral

O **Audit Summary Microservice** é uma solução enterprise para análise automatizada de atendimentos de customer service, utilizando técnicas avançadas de IA como **LLM as Judge** e **Self-Reflection** para gerar insights acionáveis sobre qualidade de atendimento.

### 🎯 Principais Funcionalidades

- **📊 Análise Inteligente**: Avaliação automática de conversas usando múltiplos modelos de IA
- **📈 Resumos Personalizados**: Geração de summaries por critério específico ou geral
- **🔍 Insights Acionáveis**: Identificação de pontos fortes, fracos, oportunidades e recomendações
- **⚡ Performance**: Processamento paralelo e otimizado para grandes volumes
- **🌐 Multi-Model**: Suporte a OpenAI, Anthropic Claude e AWS Bedrock

## 🏗️ Arquitetura

```mermaid
graph TB
    A[FastAPI + Lambda] --> B[Summary Service]
    B --> C[User Repository]
    C --> D[MySQL - Landzone]
    C --> E[MySQL - Gold]
    C --> F[AI Models]
    F --> G[OpenAI GPT]
    F --> H[Anthropic Claude]
    F --> I[AWS Bedrock]
```

### Stack Tecnológica
- **Backend**: FastAPI + Python 3.9+
- **Database**: MySQL (AWS RDS)
- **AI/ML**: OpenAI, Anthropic, AWS Bedrock, LiteLLM
- **Cloud**: AWS Lambda, RDS, Secrets Manager
- **Containerização**: Docker
- **Análise**: Jupyter Notebooks

## 🚀 Quick Start

### 🛠️ Usando Makefile (Recomendado)

```bash
# Ver todos os comandos disponíveis
make help

# Deploy rápido (com cache)
make quick-deploy

# Deploy completo (sem cache)
make full-deploy

# Deploy com timestamp
make timestamp-deploy

# Desenvolvimento local
make dev-setup
make build
make run-local
```

### Pré-requisitos
- Python 3.9+
- Docker
- AWS CLI configurado
- MySQL database access
- API keys para serviços de IA

### Instalação

```bash
# Clone o repositório
git clone <repository-url>
cd audit-summary-ms

# Criar ambiente virtual
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows

# Instalar dependências
pip install fastapi uvicorn pydantic
pip install pymysql mysql-connector-python
pip install openai anthropic litellm boto3
pip install mangum pandas numpy jupyter
```

### Configuração

1. **Copiar template de configuração**:
```bash
cp .env.example .env
```

2. **Configurar variáveis de ambiente** (ver seção [Environment Variables](#-environment-variables))

3. **Executar aplicação**:
```bash
# Desenvolvimento
uvicorn app:app --reload --host 0.0.0.0 --port 8000

# Produção
python app.py
```

4. **Acessar documentação**:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📁 Estrutura do Projeto

```
audit-summary-ms/
├── 📄 app.py                      # Entry point FastAPI + Lambda handler
├── 📁 api/
│   └── handlers/
│       └── summary_handler.py     # API endpoints principais
├── 📁 core/
│   ├── config/
│   │   └── database.py           # Configuração de banco
│   └── models/
│       └── summary_models.py     # Modelos Pydantic
├── 📁 services/
│   └── summary_service.py        # Lógica de negócio
├── 📁 repositories/
│   └── user_repository.py        # Acesso a dados + IA
├── 📁 routes/                    # ⚠️ Legacy (sendo migrado)
├── 📁 docs/                      # Documentação
├── 📄 Dockerfile                 # Container configuration
├── 📄 Auditoria_*.ipynb         # Notebooks de análise
└── 📄 requirements.txt           # Dependências Python
```

## ⚙️ Configuração

### Environment Variables

Criar arquivo `.env` na raiz do projeto:

```bash
# Database Configuration
DB_HOST=your-rds-endpoint.amazonaws.com
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_LANDZONE=audit_landzone
DB_GOLD=audit_gold

# AI Services
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
MODEL_NAME=claude-3-5-sonnet-20241022

# AWS Configuration (opcional)
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION_NAME=us-east-1

# Application
ENVIRONMENT=development
LOG_LEVEL=INFO
```

### Database Schema

O sistema utiliza duas databases MySQL:

- **`audit_landzone`**: Dados brutos de conversas
- **`audit_gold`**: Resultados processados de auditoria

## 🔌 API Endpoints

### POST `/summary/criterion`
Gera resumo baseado em critério específico.

```json
{
  "fk_id_platform": 1,
  "criterion_label": "Satisfação do Cliente",
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "attribute_name": "canal",
  "attribute_value": ["chat", "email"]
}
```

### POST `/summary/general`
Gera resumo geral de todos os critérios.

```json
{
  "fk_id_platform": 1,
  "start_date": "2025-01-01", 
  "end_date": "2025-01-31"
}
```

### Resposta Típica
```json
{
  "data": {
    "pontos_fortes": ["Atendimento rápido", "Linguagem clara"],
    "pontos_fracos": ["Falta de empatia", "Respostas genéricas"],
    "oportunidades": ["Treinamento em soft skills"],
    "recomendacoes": ["Implementar scripts personalizados"]
  },
  "metadata": {
    "total_conversations": 1500,
    "processing_time": "2.34s",
    "model_used": "claude-3-5-sonnet"
  }
}
```

## 🛠️ Desenvolvimento

### Executar Localmente

```bash
# Ativar ambiente virtual
source venv/bin/activate

# Executar com reload automático
uvicorn app:app --reload --host 0.0.0.0 --port 8000

# Executar notebooks
jupyter notebook Auditoria_criacao_resumos.ipynb
```

### Docker

```bash
# Build
docker build -t audit-summary-ms .

# Run
docker run -p 8000:8000 --env-file .env audit-summary-ms

# Run notebook
docker run -p 8888:8888 audit-summary-ms jupyter notebook --ip=0.0.0.0
```

### Testing

```bash
# Instalar dependências de teste
pip install pytest pytest-asyncio httpx

# Executar testes
pytest tests/ -v

# Coverage
pytest --cov=src tests/
```

## 🚀 Deploy

### AWS Lambda

O projeto já está configurado para deploy no AWS Lambda via **Mangum**:

```python
# app.py
from mangum import Mangum
handler = Mangum(app, lifespan="off")
```

### Docker Production

```bash
# Build para produção
docker build -t audit-summary-ms:prod .

# Deploy
docker run -d -p 80:8000 --env-file .env.prod audit-summary-ms:prod
```

## 📊 Monitoramento

### Logging

O sistema utiliza logging estruturado em JSON:

```json
{
  "timestamp": "2025-01-20T10:00:00Z",
  "level": "INFO",
  "message": "Query executada em 0.1234 segundos", 
  "module": "user_repository",
  "request_id": "req-123"
}
```

### Métricas

- **Performance**: Tempo de execução de queries (decorator `@measure_time`)
- **AI Models**: Response time por modelo
- **API**: Request/response metrics
- **Errors**: Rate e tipos de erro

## 🔒 Segurança

### ⚠️ Pontos de Atenção

1. **Credenciais**: Migrar hardcoded credentials para environment variables
2. **CORS**: Configurar origins específicos para produção
3. **Rate Limiting**: Implementar para APIs de IA
4. **Input Validation**: Validação rigorosa de inputs
5. **Secrets**: Usar AWS Secrets Manager

### Boas Práticas

```python
# ✅ Usar environment variables
DB_PASSWORD = os.getenv("DB_PASSWORD")

# ✅ Validação de input
class SummaryRequest(BaseModel):
    fk_id_platform: int = Field(gt=0)
    start_date: str = Field(regex=r'^\d{4}-\d{2}-\d{2}$')

# ✅ Error handling
try:
    result = await ai_service.generate_summary()
except Exception as e:
    logger.error(f"AI service error: {e}")
    raise HTTPException(status_code=500)
```

## 🤝 Contribuição

### Workflow

1. Fork o projeto
2. Criar branch feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit mudanças (`git commit -am 'Add nova funcionalidade'`)
4. Push para branch (`git push origin feature/nova-funcionalidade`)
5. Criar Pull Request

### Code Style

```bash
# Formatação
black src/ tests/
isort src/ tests/

# Linting
flake8 src/ tests/
mypy src/
```

### Testes

- **Unit Tests**: Para cada service e repository
- **Integration Tests**: Para endpoints e database
- **E2E Tests**: Para fluxos completos

## 🔄 Workflow de Análise

### 1. Coleta de Dados
```python
# Dados extraídos de duas databases
audit_landzone  # Conversas brutas, atributos, ações
audit_gold      # Resultados processados de auditoria
```

### 2. Processamento IA
```python
# Pipeline de análise
1. Extração de conversas por critério
2. Classificação usando LLM as Judge
3. Self-reflection para validação
4. Geração de insights estruturados
```

### 3. Geração de Resumos
```python
# Outputs estruturados
{
  "pontos_fortes": [...],
  "pontos_fracos": [...],
  "oportunidades": [...],
  "recomendacoes": [...]
}
```

## 🎯 Roadmap

### Versão 1.1
- [ ] Implementar connection pooling
- [ ] Adicionar cache Redis
- [ ] Migrar credenciais para Secrets Manager
- [ ] Implementar rate limiting

### Versão 1.2
- [ ] Suporte a múltiplos idiomas
- [ ] Dashboard interativo
- [ ] Webhooks para notificações
- [ ] Processamento assíncrono

### Versão 2.0
- [ ] Multi-tenant architecture
- [ ] Real-time analysis
- [ ] Advanced ML models
- [ ] Custom criteria builder

## 🆘 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão Database
```bash
# Verificar conectividade
telnet audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com 3306

# Verificar credenciais
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD
```

#### 2. API Keys Inválidas
```bash
# Testar OpenAI
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models

# Testar Anthropic
curl -H "x-api-key: $ANTHROPIC_API_KEY" https://api.anthropic.com/v1/messages
```

#### 3. Import Errors
```python
# Verificar PYTHONPATH
import sys
print(sys.path)

# Adicionar ao path se necessário
sys.path.append('/path/to/audit-summary-ms')
```

## 📊 Performance Benchmarks

### Métricas Típicas
- **Database Query**: 50-200ms
- **AI Model Response**: 1-5s (Claude), 2-8s (GPT)
- **Full Summary Generation**: 10-30s
- **Concurrent Requests**: 10-50 (dependendo do modelo)

### Otimizações
```python
# Paralelização de queries
with ThreadPoolExecutor(max_workers=4) as executor:
    futures = [
        executor.submit(generate_pontos_fracos),
        executor.submit(generate_pontos_fortes),
        executor.submit(generate_oportunidades),
        executor.submit(generate_recomendacoes)
    ]
```

## 🛠️ Makefile Commands

### Comandos Principais

| Comando | Descrição |
|---------|-----------|
| `make help` | Mostra todos os comandos disponíveis |
| `make quick-deploy` | Deploy rápido (com cache) |
| `make full-deploy` | Deploy completo (sem cache) |
| `make timestamp-deploy` | Deploy com tag de timestamp |
| `make build` | Build da imagem Lambda |
| `make build-prod` | Build da imagem de produção |
| `make run-local` | Roda container Lambda localmente |
| `make run-prod` | Roda container de produção localmente |
| `make test` | Executa testes |
| `make clean` | Remove imagens locais |

### Workflows Comuns

```bash
# Primeiro deploy
make status
make full-deploy

# Desenvolvimento
make dev-setup
make build-cached
make run-local

# Release de produção
make release TAG=v1.0.0

# Debugging
make shell
make logs
```

Para mais detalhes, consulte [MAKEFILE_USAGE.md](docs/MAKEFILE_USAGE.md).

## 📚 Documentação Adicional

- [🛠️ Makefile Usage Guide](docs/MAKEFILE_USAGE.md)
- [🔧 PyCharm Setup Guide](docs/PYCHARM_SETUP.md)
- [🤖 Agent Context](docs/AGENT_CONTEXT.md)
- [📊 API Documentation](http://localhost:8000/docs)
- [🐳 Docker Guide](docs/DOCKER.md)
- [🚀 Deployment Guide](docs/DEPLOYMENT.md)
- [📋 Migration Summary](docs/MIGRATION_SUMMARY.md)
- [🚀 Deployment Summary](docs/DEPLOYMENT_SUMMARY.md)
- [🛠️ Makefile Implementation](docs/MAKEFILE_IMPLEMENTATION_SUMMARY.md)

## 🤝 Suporte

### Contatos
- **Tech Lead**: Rafael Carvalho Ferreira
- **DevOps**: [Inserir contato]
- **Product Owner**: [Inserir contato]

### Canais de Comunicação
- **Slack**: #audit-summary-dev
- **Email**: <EMAIL>
- **Issues**: GitHub Issues

## 📄 Licença

Este projeto é propriedade da empresa. Todos os direitos reservados.

---

**Desenvolvido com ❤️ para análise inteligente de customer service**

**Versão**: 1.0 | **Data**: 2025-01-20 | **Autor**: Rafael Carvalho Ferreira
