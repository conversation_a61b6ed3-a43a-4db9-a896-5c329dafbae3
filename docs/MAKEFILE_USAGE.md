# 🛠️ Makefile Usage Guide - Audit Summary Microservice

## 📋 Overview

Este Makefile fornece comandos automatizados para build, deploy e gerenciamento do microserviço audit-summary-ms no AWS ECR e Lambda.

## 🚀 Quick Start

```bash
# Ver todos os comandos disponíveis
make help

# Deploy rápido (com cache)
make quick-deploy

# Deploy completo (sem cache)
make full-deploy

# Deploy com timestamp
make timestamp-deploy
```

## 📚 Comandos Principais

### 🔧 Setup e Verificação

| Comando | Descrição |
|---------|-----------|
| `make help` | Mostra todos os comandos disponíveis |
| `make check-docker` | Verifica se Docker está rodando |
| `make check-aws` | Verifica se AWS CLI está configurado |
| `make status` | Mostra status atual do ambiente |

### 🏗️ Build

| Comando | Descrição |
|---------|-----------|
| `make build` | Build da imagem Lambda (sem cache) |
| `make build-cached` | Build da imagem Lambda (com cache) |
| `make build-prod` | Build da imagem de produção (standalone) |

### 🚢 Deploy

| Comando | Descrição |
|---------|-----------|
| `make login` | Login no ECR |
| `make push` | Push da imagem para ECR |
| `make build-and-push` | Build + push (processo completo) |
| `make build-cached-and-push` | Build com cache + push |
| `make quick-deploy` | Deploy rápido (alias para build-cached-and-push) |
| `make full-deploy` | Deploy completo (alias para build-and-push) |
| `make timestamp-deploy` | Deploy com tag de timestamp |

### 🏷️ Versionamento

| Comando | Descrição | Exemplo |
|---------|-----------|---------|
| `make tag TAG=<version>` | Cria tag personalizada | `make tag TAG=v1.2.0` |
| `make push-tag TAG=<version>` | Push de tag específica | `make push-tag TAG=v1.2.0` |
| `make release TAG=<version>` | Release completo | `make release TAG=v1.2.0` |

### 🧪 Desenvolvimento e Testes

| Comando | Descrição |
|---------|-----------|
| `make dev-setup` | Configura ambiente de desenvolvimento |
| `make test` | Executa testes (se disponíveis) |
| `make lint` | Executa linting do código |
| `make run-local` | Roda container Lambda localmente (porta 8080) |
| `make run-prod` | Roda container de produção localmente (porta 8000) |
| `make shell` | Abre shell no container Lambda |
| `make shell-prod` | Abre shell no container de produção |

### 🔍 Inspeção e Logs

| Comando | Descrição |
|---------|-----------|
| `make inspect` | Inspeciona a imagem construída |
| `make logs` | Mostra informações do repositório ECR |
| `make size` | Mostra tamanhos das imagens |
| `make history` | Mostra histórico de build da imagem |

### 🧹 Limpeza

| Comando | Descrição |
|---------|-----------|
| `make clean` | Remove imagens locais e limpa sistema |

## 🎯 Workflows Comuns

### 1. Primeiro Deploy
```bash
# Verificar ambiente
make status

# Deploy completo
make full-deploy
```

### 2. Deploy de Desenvolvimento
```bash
# Deploy rápido para testes
make quick-deploy
```

### 3. Release de Produção
```bash
# Release versionado
make release TAG=v1.0.0
```

### 4. Deploy com Timestamp
```bash
# Deploy com tag de data/hora
make timestamp-deploy
```

### 5. Desenvolvimento Local
```bash
# Configurar ambiente
make dev-setup

# Testar localmente
make build
make run-local

# Em outro terminal, testar a API
curl http://localhost:8080/health
```

### 6. Debugging
```bash
# Construir e inspecionar
make build
make inspect

# Abrir shell no container
make shell

# Ver logs do ECR
make logs
```

## ⚙️ Configuração

### Variáveis do Makefile

```makefile
ECR_REGISTRY = 637423516204.dkr.ecr.us-east-1.amazonaws.com
ECR_REPOSITORY = audit-summary-ms
AWS_REGION = us-east-1
IMAGE_TAG = latest
PLATFORM = linux/amd64
```

### Personalização

Para usar com diferentes configurações, você pode sobrescrever as variáveis:

```bash
# Usar tag diferente
make build IMAGE_TAG=dev

# Usar região diferente
make login AWS_REGION=us-west-2
```

## 🐳 Tipos de Imagem

### Lambda Image (Dockerfile)
- **Uso**: AWS Lambda
- **Comando**: `make build`
- **Porta**: 8080 (localmente)
- **Handler**: `app.handler`

### Production Image (Dockerfile.prod)
- **Uso**: Standalone deployment
- **Comando**: `make build-prod`
- **Porta**: 8000
- **Servidor**: FastAPI + Uvicorn

## 🔧 Troubleshooting

### Problemas Comuns

1. **Docker não encontrado**
   ```bash
   make check-docker
   ```

2. **AWS não configurado**
   ```bash
   make check-aws
   aws configure
   ```

3. **Erro de login ECR**
   ```bash
   make login
   ```

4. **Imagem muito grande**
   ```bash
   make size
   make clean
   ```

5. **Build falhando**
   ```bash
   make build  # Sem cache
   make history  # Ver histórico
   ```

## 📝 Exemplos Práticos

### Deploy Completo
```bash
# 1. Verificar ambiente
make status

# 2. Login no ECR
make login

# 3. Build e push
make build-and-push

# 4. Verificar resultado
make logs
```

### Desenvolvimento Iterativo
```bash
# 1. Setup inicial
make dev-setup

# 2. Desenvolvimento...
# (fazer mudanças no código)

# 3. Teste local
make build-cached
make run-local

# 4. Deploy rápido
make quick-deploy
```

### Release de Versão
```bash
# 1. Finalizar desenvolvimento
make test
make lint

# 2. Release
make release TAG=v2.1.0

# 3. Verificar
make logs
```

## 🎨 Cores no Output

O Makefile usa cores para melhor visualização:
- 🔵 **Azul**: Informações e títulos
- 🟢 **Verde**: Sucesso
- 🟡 **Amarelo**: Avisos e configurações
- 🔴 **Vermelho**: Erros

## 📞 Suporte

Para problemas ou dúvidas sobre o Makefile:
1. Execute `make help` para ver todos os comandos
2. Execute `make status` para verificar o ambiente
3. Consulte os logs com `make logs`
