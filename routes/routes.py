from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import Optional
import json
from controllers.controllers import DatabaseConfig, DatabaseConnectionFactory, UserRepository
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

class GeneralSummaryRequest(BaseModel):
    fk_id_platform: int
    start_date: str
    end_date: str
    attribute_name: Optional[str] = None
    attribute_value: Optional[list[str]] = None

class SummaryRequest(BaseModel):
    fk_id_platform: int
    criterion_label: Optional[str] = None
    start_date: str
    end_date: str
    attribute_name: Optional[str] = None
    attribute_value: Optional[list[str]] = None

def get_user_repository(db_name: str):
    db_config = DatabaseConfig(
        host="audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com",
        database=db_name,
        user="devdb",
        password="jtNr6=LdR+R6aF4-d~J"
    )
    factory = DatabaseConnectionFactory(db_config)
    connection = factory.create_connection()
    if connection:
        return UserRepository(connection)
    else:
        raise Exception("Não foi possível conectar ao banco de dados")

@router.post("/summary/criterion")
async def get_summary_by_criterion(request: SummaryRequest):
    logger.info(f"Received request: {request}")
    
    user_repo_landzone = get_user_repository("audit_landzone")
    user_repo_gold = get_user_repository("audit_gold")

    negative_examples = user_repo_landzone.get_conversation(request.fk_id_platform, "não", request.criterion_label, request.start_date, request.end_date)
    criterion_results_raw = user_repo_gold.get_criterion_results(request.fk_id_platform, request.criterion_label, request.start_date, request.end_date)
    positive_examples = user_repo_landzone.get_conversation(request.fk_id_platform, "sim", request.criterion_label, request.start_date, request.end_date)

    if len(negative_examples) == 0 and len(positive_examples) == 0:
        return {
            "statusCode": 200,
            "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            },
            "body" : json.dumps({"data":"Ainda não há interações suficientes para gerar insights"})
        }

    elif "tempo" in request.criterion_label.lower():
        negative_examples_1 = user_repo_landzone.get_examples(negative_examples)
        positive_examples_1 = user_repo_landzone.get_examples(positive_examples)

        criterion_rules_list: list = user_repo_landzone.get_criterion_rules(request.criterion_label, request.fk_id_platform, request.start_date, request.end_date)
        processed_rules_str = user_repo_landzone.process_rules(criterion_rules_list)
        print(processed_rules_str)
        tmr_mean_str = user_repo_landzone.process_time_criterion_results(request.fk_id_platform, request.start_date, request.end_date, request.criterion_label)
        print(tmr_mean_str)
        response = user_repo_landzone.generate_time_summary( request.criterion_label,tmr_mean_str, positive_examples_1, negative_examples_1, processed_rules_str)
        
        negative_examples = user_repo_landzone.get_examples(negative_examples)
        positive_examples = user_repo_landzone.get_examples(positive_examples)
        
        user_repo_gold.close_conn()
        user_repo_landzone.close_conn()
        return {
        "statusCode": 200,
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        },
        "body": json.dumps({"data": json.loads(response),"positive_examples": positive_examples, "negative_examples": negative_examples})
    }
    params = user_repo_landzone.get_criterion_params(request.criterion_label, request.fk_id_platform)
    processed_results = user_repo_landzone.process_results(criterion_results_raw, negative_examples, positive_examples, params, request.fk_id_platform)
    
    criterion_rules_list: list = user_repo_landzone.get_criterion_rules(request.criterion_label, request.fk_id_platform, request.start_date, request.end_date)
    processed_rules_str = user_repo_landzone.process_rules(criterion_rules_list)

    if len(criterion_rules_list) > 0:
        response = user_repo_landzone.generate_summary( processed_results["criterion_name"],
        processed_results['criterion_result_string'],
        processed_results['positive_conversation'],
        processed_results['negative_conversation'],
        processed_rules_str   )
    print(criterion_rules_list)
    negative_sample = user_repo_landzone.get_examples(negative_examples)
    positve_sample = user_repo_landzone.get_examples(positive_examples)

    user_repo_gold.close_conn()
    print(response)
    response = {
        "data": json.loads(response),
        "positive_examples": positve_sample, 
        "negative_examples": negative_sample
    }
    return {
        "statusCode": 200,
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        },
        "body": json.dumps(response) 
    }

@router.post("/summary/general")
async def get_general_summary(request: GeneralSummaryRequest):
    logger.info(f"Received request: {request}")
    user_repo_landzone = get_user_repository("audit_landzone")
    user_repo_gold = get_user_repository("audit_gold")

    criterion_results_raw = user_repo_gold.get_all_criterion_results(request.fk_id_platform, request.start_date, request.end_date)
    criterion_string = user_repo_landzone.process_criterion_results(criterion_results_raw)
    response = user_repo_landzone.generate_summary_all(criterion_string )

    response = {
        "data": json.loads(response)
    }
    user_repo_gold.close_conn()
    user_repo_landzone.close_conn()
    return {
        "statusCode": 200,
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        },
        "body": json.dumps(response) 
    }