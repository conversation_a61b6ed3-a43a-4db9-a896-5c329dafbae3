import os
import boto3
import json
import logging
from openai import OpenAI
from functools import wraps
import time

# Configure logging
logger = logging.getLogger(__name__)

def get_secret():
    """
    Retrieve secrets from AWS Secrets Manager.

    For Lambda deployment: Uses IAM Role attached to the Lambda instance (recommended).
    For local development: Falls back to AWS credentials file or environment variables.
    """
    try:
        secret_name = os.getenv("AWS_SCRT_NAME", "prod/summary")
        region_name = os.getenv("AWS_RGN", "us-east-1")

        client = boto3.client("secretsmanager", region_name=region_name)
        resp = client.get_secret_value(SecretId=secret_name)
        secrets = json.loads(resp["SecretString"])

        logger.info(f"Successfully retrieved secrets from AWS Secrets Manager: {secret_name}")
        return secrets
    except Exception as e:
        logger.warning(f"Failed to retrieve secrets from AWS Secrets Manager: {e}")
        logger.info("Falling back to environment variables")
        return {}

# Load secrets from AWS Secrets Manager
_secrets = get_secret()

class OpenAIConfig:
    OPENAI_API_KEY = _secrets.get("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY")
    DEEPSEEK_API_KEY = _secrets.get("DEEPSEEK_API_KEY") or os.getenv("DEEPSEEK_API_KEY")
    ANTHROPIC_API_KEY = _secrets.get("ANTHROPIC_API_KEY") or os.getenv("ANTHROPIC_API_KEY")

def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"AI request {func.__name__} executada em {duration:.4f} segundos")
        return result
    return wrapper

class OpenAIRepository:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = OpenAIConfig()

    def _get_client(self, model: str):
        """Get appropriate OpenAI client based on model"""
        if "deepseek" in model.lower():
            return OpenAI(
                api_key=self.config.DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com"
            )
        else:
            return OpenAI(api_key=self.config.OPENAI_API_KEY)

    @measure_time
    def chat_completion_request(self, messages: list, model: str, temperature: float = 0):
        """Make chat completion request to appropriate AI service"""
        client = self._get_client(model)
        try:
            response = client.chat.completions.create(
                messages=messages,
                model=model,
                temperature=temperature,
                response_format={"type": "json_object"},
            )
            return response.choices[0].message.content, response.usage
        except Exception as e:
            self.logger.error(f"Unable to generate ChatCompletion response: {e}")
            return str(e), None